<configuration scan="true" scanPeriod="10 seconds">
    <springProperty scope="context" name="logSaveDays" source="config.logSaveDays"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level --- [%15.15(%thread)] %-40.40(%logger{40}) : %msg%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>log/info.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${log.level}</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>log/info-%d{yyyyMMdd}.log.%i</fileNamePattern>
            <maxFileSize>500MB</maxFileSize>
            <maxHistory>${logSaveDays}</maxHistory>
        </rollingPolicy>
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level --- [%15.15(%thread)] %-40.40(%logger{40}) : %msg%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <root level="ERROR">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO_FILE"/>
    </root>

    <logger name="com.hanvon" level="INFO" additivity="false">
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="STDOUT"/>
    </logger>

</configuration>