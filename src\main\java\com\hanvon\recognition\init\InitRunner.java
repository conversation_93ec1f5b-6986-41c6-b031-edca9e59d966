package com.hanvon.recognition.init;

import com.hanvon.recognition.config.SystemConfig;
import com.hanvon.recognition.jni.JniApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class InitRunner implements CommandLineRunner {

    private final SystemConfig systemConfig;

    private final RabbitListenerEndpointRegistry rabbitListenerEndpointRegistry;

    @Override
    public void run(String... args) {
        if (StringUtils.contains(System.getProperty("os.name"), "Linux")) {
            log.info("开始初始化...");
            System.load(systemConfig.getSoPath());
            JniApi.handle = JniApi.init(systemConfig.getInitPath());
            log.info("初始化完成,handle:{}", JniApi.handle);
        }
        rabbitListenerEndpointRegistry.getListenerContainer("consumer").start();
    }
}
