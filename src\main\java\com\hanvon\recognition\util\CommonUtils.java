package com.hanvon.recognition.util;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

public class CommonUtils {

    public static Throwable extractRealException(Throwable throwable) {
        if (throwable instanceof CompletionException || throwable instanceof ExecutionException) {
            if (throwable.getCause() != null) {
                return throwable.getCause();
            }
        }
        return throwable;
    }

    public static String exceptionToString(Throwable e) {
        if (Objects.isNull(e)) {
            return "";
        }
        StringWriter stringWriter = new StringWriter();
        e.printStackTrace(new PrintWriter(stringWriter));
        return stringWriter.toString();
    }

    public static String tdFormat(LocalDateTime startTime, LocalDateTime endTime) {
        Duration duration = Duration.between(startTime, endTime);
        StringJoiner stringJoiner = new StringJoiner("");
        long millis = duration.toMillis();
        long days = TimeUnit.MILLISECONDS.toDays(millis);
        if (days > 0) {
            stringJoiner.add(days + "天");
            millis -= TimeUnit.DAYS.toMillis(days);
        }
        long hours = TimeUnit.MILLISECONDS.toHours(millis);
        if (hours > 0) {
            stringJoiner.add(hours + "小时");
            millis -= TimeUnit.HOURS.toMillis(hours);
        }
        long minutes = TimeUnit.MILLISECONDS.toMinutes(millis);
        if (minutes > 0) {
            stringJoiner.add(minutes + "分钟");
            millis -= TimeUnit.MINUTES.toMillis(minutes);
        }
        long seconds = TimeUnit.MILLISECONDS.toSeconds(millis);
        if (seconds > 0) {
            stringJoiner.add(seconds + "秒");
            millis -= TimeUnit.SECONDS.toMillis(seconds);
        }
        if (millis > 0) {
            stringJoiner.add(millis + "毫秒");
        }
        return stringJoiner.toString();
    }
}
