/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_hanvon_recognition_jni_JniApi */

#ifndef _Included_com_hanvon_recognition_jni_JniApi
#define _Included_com_hanvon_recognition_jni_JniApi
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_hanvon_recognition_jni_JniApi
 * Method:    init
 * Signature: (Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_com_hanvon_recognition_jni_JniApi_init
  (JNIEnv *, jclass, jstring);

/*
 * Class:     com_hanvon_recognition_jni_JniApi
 * Method:    videoProcessing
 * Signature: (JLjava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_hanvon_recognition_jni_JniApi_videoProcessing
  (JNIEnv *, jclass, jlong, jstring);

/*
 * Class:     com_hanvon_recognition_jni_JniApi
 * Method:    absenceDetermine
 * Signature: (JLjava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_hanvon_recognition_jni_JniApi_absenceDetermine
  (JNIEnv *, jclass, jlong, jstring);

/*
 * Class:     com_hanvon_recognition_jni_JniApi
 * Method:    sensitiveWordsFiltering
 * Signature: (JLjava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_hanvon_recognition_jni_JniApi_sensitiveWordsFiltering
  (JNIEnv *, jclass, jlong, jstring);

#ifdef __cplusplus
}
#endif
#endif
