package com.hanvon.recognition.pojo.jni;

import com.hanvon.recognition.pojo.QcInfo;
import com.hanvon.recognition.pojo.SensWordTemplate;
import com.hanvon.recognition.pojo.voice.Asr;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class SensitiveWordsFilteringRequestDto {
    private String transId;
    private String transUuid;
    private List<SensWordTemplate> sensWordTemplate;
    private Asr asr;
    private QcInfo qcInfo;
}
