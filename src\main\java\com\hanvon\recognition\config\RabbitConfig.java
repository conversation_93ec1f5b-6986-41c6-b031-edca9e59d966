package com.hanvon.recognition.config;

import com.rabbitmq.client.DefaultSaslConfig;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.File;
import java.security.KeyStore;
import java.util.Base64;

@Configuration
@RequiredArgsConstructor
public class RabbitConfig {

    public static final String EXCHANGE = "AIQualityMessageExchange";

    public static final String QUEUE_REQUEST = "ai_queue";

    public static final String QUEUE_RESPONSE = "ai_ResQueue";

    public static final String ROUTING_KEY_REQUEST = "AIQualityMessage";

    public static final String ROUTING_KEY_RESPONSE = "AIResMessage";

    private final SystemConfig systemConfig;

    @Bean(QUEUE_REQUEST)
    public Queue requestQueue() {
        return new Queue(QUEUE_REQUEST);
    }

    @Bean(QUEUE_RESPONSE)
    public Queue responseQueue() {
        return new Queue(QUEUE_RESPONSE);
    }

    @Bean(EXCHANGE)
    public TopicExchange rabbitmqDemoDirectExchange() {
        return new TopicExchange(EXCHANGE);
    }

    @Bean("BINDING_REQUEST")
    public Binding bindingRequest(@Qualifier(QUEUE_REQUEST) Queue queue, @Qualifier(EXCHANGE) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTING_KEY_REQUEST).noargs();
    }

    @Bean("BINDING_RESPONSE")
    public Binding bindingResponse(@Qualifier(QUEUE_RESPONSE) Queue queue, @Qualifier(EXCHANGE) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTING_KEY_RESPONSE).noargs();
    }

    @Bean
    @SneakyThrows
    public ConnectionFactory connectionFactory(
//            @Value("${spring.rabbitmq.host}") String host,
//            @Value("${spring.rabbitmq.port}") int port,
            @Value("${spring.rabbitmq.addresses}") String addresses,
            @Value("${spring.rabbitmq.username}") String username,
            @Value("${spring.rabbitmq.password}") String password,
            @Value("${spring.rabbitmq.ssl.enabled:false}") boolean sslEnabled,
            @Value("${spring.rabbitmq.ssl.key-store-password:}") String keyStorePassword,
            @Value("${spring.rabbitmq.ssl.trust-store-password:}") String trustStorePassword) {

        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
//        connectionFactory.setHost(host);
//        connectionFactory.setPort(port);
        connectionFactory.setAddresses(addresses);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(new String(Base64.getDecoder().decode(password)));

        // Configure SSL only if enabled
        if (sslEnabled) {
            KeyStore ks = KeyStore.getInstance("PKCS12");
            ks.load(FileUtils.openInputStream(new File(systemConfig.getJarPath() + "/ssl/rabbitmq-client.keycert.p12")), keyStorePassword.toCharArray());
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(ks, keyStorePassword.toCharArray());

            KeyStore tks = KeyStore.getInstance("JKS");
            tks.load(FileUtils.openInputStream(new File(systemConfig.getJarPath() + "/ssl/rabbitmqTrustStore")), trustStorePassword.toCharArray());
            TrustManagerFactory tmf = TrustManagerFactory.getInstance("SunX509");
            tmf.init(tks);

            SSLContext ctx = SSLContext.getInstance("TLSv1.2");
            ctx.init(kmf.getKeyManagers(), tmf.getTrustManagers(), null);

            connectionFactory.getRabbitConnectionFactory().useSslProtocol(ctx);
            connectionFactory.getRabbitConnectionFactory().setSaslConfig(DefaultSaslConfig.EXTERNAL);
        }

        return connectionFactory;
    }
}
