spring:
  profiles:
    active: test
  application:
    name: video-recognition-service
  rabbitmq:
    listener:
      simple:
        concurrency: 1
        max-concurrency: 1
        prefetch: 1
        missing-queues-fatal: false
        retry:
          enabled: true
    dynamic: false
    ssl:
      enabled: false

logging:
  config: classpath:logback-spring.xml

config:
  jarPath:
  soPath:
  initPath:
  logSaveDays: 7
  deleteTemp:

fdfs:
  so-timeout: 1501
  connect-timeout: 601
  thumb-image:
    width: 150
    height: 150
