package com.hanvon.recognition.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hanvon.recognition.config.VoiceConfig;
import com.hanvon.recognition.pojo.ResponseDto;
import com.hanvon.recognition.pojo.voice.*;
import com.hanvon.recognition.util.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@RequiredArgsConstructor
@Slf4j
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
public class VoiceRemoteProcessor {

    private final static String format = "auf=audio/L16;rate=8000; aue=raw";

    private final static int sliceSize = 1024 * 190;

    private final static int smallFileSliceSize = 1024 * 20;

    private final static int smallFileThreshold = 1024 * 381;

    private final static int asrTimeoutTick = 480;

    private final static int retryTimesLimit = 5;

    private final RestTemplate restTemplate;

    private final VoiceConfig voiceConfig;

    private final ObjectMapper objectMapper;

    private String sid;

    private List<String> cookie;

    public static List<byte[]> splitByteArrayToList(byte[] data, int chunkSize) {
        int length = data.length;
        List<byte[]> result = new ArrayList<>();
        int start = 0;
        for (int i = 0; start < length; i++) {
            int end = Math.min(length, start + chunkSize);
            result.add(Arrays.copyOfRange(data, start, end));
            start += chunkSize;
        }
        return result;
    }

    // 前三类请求循环5次,如果5次都失败,直接异常
    // sid不一致且还未finish,直接异常
    // 未finish前报错,发送end请求后退出
    public Asr remoteProcessingVoice(ResponseDto responseDto, String processedAudioPath) throws IOException, InterruptedException {
        byte[] audioFileBytes = FileUtils.readFileToByteArray(new File(processedAudioPath));
        int actualSliceSize = audioFileBytes.length < smallFileThreshold ? smallFileSliceSize : sliceSize;
        List<byte[]> audioSlices = splitByteArrayToList(audioFileBytes, actualSliceSize);
        int size = audioSlices.size();
        log.info("{},调用远程语音转文本数据长度为{}字节,切片大小为{}字节,切片成{}份", responseDto.print(), audioFileBytes.length, actualSliceSize, size);
        String status = "";
        for (int i = 0; i < size; i++) {
            byte[] audioSlice = audioSlices.get(i);
            int retryTimes = 0;
            int code = -1;
            ResponseEntity<VoiceResponseDto> response = null;
            while (code != 0 && retryTimes < retryTimesLimit) {
                if (i == 0) {
                    status = "begin";
                } else if (i == size - 1) {
                    status = "end";
                } else {
                    status = "continue";
                }
                LocalDateTime startTime = LocalDateTime.now();
                try {
                    response = request(status, audioSlice);
                } finally {
                    if (!Objects.isNull(response)) {
                        log.info("{},sid:{},调用远程语音转文本,切片id:{},请求类型:{}{},耗时{},httpStatusCode:{},responseBody:{}", responseDto.print(), sid, i, status, retryTimes == 0 ? "" : ",重试第" + retryTimes + "次", CommonUtils.tdFormat(startTime, LocalDateTime.now()), response.getStatusCode(), response.getBody());
                    } else {
                        log.error("{},sid:{},调用远程语音转文本,切片id:{},请求类型:{},response为空", responseDto.print(), sid, i, status);
                        retryTimes++;
                        continue;
                    }
                }
                if (response.getStatusCode() != HttpStatus.OK) {
                    log.error("{},sid:{},调用远程语音转文本失败,切片id:{},请求类型:{},httpStatusCode不为200", responseDto.print(), sid, i, status);
                    retryTimes++;
                    continue;
                }
                VoiceResponseDto responseBody = response.getBody();
                if (Objects.isNull(responseBody) || Objects.isNull(responseBody.getCode()) || Objects.isNull(responseBody.getFinish())) {
                    log.error("{},sid:{},调用远程语音转文本失败,切片id:{},请求类型:{},responseBody关键内容为空", responseDto.print(), sid, i, status);
                    retryTimes++;
                    continue;
                }
                if (!StringUtils.equals(responseBody.getSid(), sid)) {
                    log.error("{},sid:{},调用远程语音转文本失败,切片id:{},请求类型:{},sid不一致", responseDto.print(), sid, i, status);
                    request("end");
                    return null;
                }
                if (responseBody.getFinish()) {
                    if (!StringUtils.equals(status, "end")) {
                        log.error("{},sid:{},调用远程语音转文本失败,切片id:{},请求类型:{},但是finish为true", responseDto.print(), sid, i, status);
                        return null;
                    }
                    if (responseBody.getCode() != 0) {
                        log.error("{},sid:{},调用远程语音转文本失败,切片id:{},请求类型:{},finish为true,但是code为:{},message为:{}", responseDto.print(), sid, i, status, responseBody.getCode(), responseBody.getMessage());
                        return null;
                    }

                    String result = responseBody.getResult();
                    if (StringUtils.isEmpty(result)) {
                        log.error("{},sid:{},调用远程语音转文本失败,切片id:{},请求类型:{},finish为true,但是result为空", responseDto.print(), sid, i, status);
                        return null;
                    }
                    log.info("{},sid:{},调用远程语音转文本完成,请求类型:{}", responseDto.print(), sid, status);
                    return objectMapper.readValue(result, Asr.class);
                }
                code = responseBody.getCode();
                retryTimes++;
            }
            if (code != 0) {
                log.error("{},sid:{},重试次数已达上限,调用远程语音转文本失败,切片id:{},请求类型:{}", responseDto.print(), sid, i, status);
                request("end");
                return null;
            }
        }
        TimeUnit.SECONDS.sleep(5);
        for (int i = 0; i < asrTimeoutTick; i++) {
            status = "blank";
            ResponseEntity<VoiceResponseDto> response = null;
            LocalDateTime startTime = LocalDateTime.now();
            try {
                response = request(status, null);
            } finally {
                if (!Objects.isNull(response)) {
                    log.info("{},sid:{},调用远程语音转文本,切片id:{},请求类型:{}{},耗时{},httpStatusCode:{},responseBody:{}", responseDto.print(), sid, i, status, i == 0 ? "" : ",重试第" + i + "次", CommonUtils.tdFormat(startTime, LocalDateTime.now()), response.getStatusCode(), response.getBody());
                } else {
                    log.error("{},sid:{},调用远程语音转文本,切片id:{},请求类型:{},response为空", responseDto.print(), sid, i, status);
                    request("end");
                    return null;
                }
            }
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("{},sid:{},调用远程语音转文本失败,请求类型:{},重试次数:{},httpStatusCode:{}", responseDto.print(), sid, status, i, response.getStatusCode());
                request("end");
                return null;
            }
            VoiceResponseDto responseBody = response.getBody();
            if (Objects.isNull(responseBody) || Objects.isNull(responseBody.getCode()) || Objects.isNull(responseBody.getFinish())) {
                log.error("{},sid:{},调用远程语音转文本失败,请求类型:{},重试次数:{},responseBody关键内容为空", responseDto.print(), sid, status, i);
                request("end");
                return null;
            }
            if (!StringUtils.equals(responseBody.getSid(), sid)) {
                log.error("{},sid:{},调用远程语音转文本失败,请求类型:{},重试次数:{},sid不一致", responseDto.print(), sid, status, i);
                request("end");
                return null;
            }
            if (responseBody.getFinish()) {
                if (responseBody.getCode() != 0) {
                    log.error("{},sid:{},调用远程语音转文本失败,请求类型:{},重试次数:{},finish为true,但是code为:{},message为:{}", responseDto.print(), sid, status, i, responseBody.getCode(), responseBody.getMessage());
                    return null;
                }
                String result = responseBody.getResult();
                if (StringUtils.isEmpty(result)) {
                    log.error("{},sid:{},调用远程语音转文本失败,请求类型:{},重试次数:{},finish为true,但是result为空", responseDto.print(), sid, status, i);
                    return null;
                }
                log.info("{},sid:{},调用远程语音转文本完成,请求类型:{}", responseDto.print(), sid, status);
                return objectMapper.readValue(result, Asr.class);
            }
            TimeUnit.SECONDS.sleep(5);
        }
        log.error("{},sid:{},调用远程语音转文本失败,请求类型:{},重试次数超过设定值{}", responseDto.print(), sid, status, asrTimeoutTick);
        request("end");
        return null;
    }

    // begin、continue、end
    // blank(自定义的空包)
    public ResponseEntity<VoiceResponseDto> request(String status, byte[] content) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        if (cookie != null && !cookie.isEmpty()) {
            httpHeaders.put(HttpHeaders.COOKIE, cookie);
        }
        final Common common = new Common(voiceConfig.getAppid(), voiceConfig.getSceneid(), voiceConfig.getAbilityid(), voiceConfig.getUid());
        Session session;
        Payload[] payloads;
        if (StringUtils.equals(status, "blank")) {
            session = new Session(sid, "end", null);
            payloads = new Payload[]{};
        } else {
            session = new Session(sid, status, null);
            payloads = new Payload[]{new Payload("1", "audio", status, Base64.getEncoder().encodeToString(content), format)};
        }
        final Params params = new Params("off");
        HttpEntity<VoiceRequestDto> httpEntity = new HttpEntity<>(new VoiceRequestDto(common, session, params, payloads), httpHeaders);
        ResponseEntity<VoiceResponseDto> responseEntity = restTemplate.postForEntity(voiceConfig.getUrl(), httpEntity, VoiceResponseDto.class);
        if (StringUtils.equals(status, "begin")) {
            sid = responseEntity.getBody().getSid();
            cookie = responseEntity.getHeaders().get("Set-Cookie");
        }
        return responseEntity;
    }

    public ResponseEntity<VoiceResponseDto> request(String status) {
        return request(status, new byte[0]);
    }
}
