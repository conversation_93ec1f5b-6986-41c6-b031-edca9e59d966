package com.hanvon.recognition.pojo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum ErrorResult {

    REQUEST_CONVERSION_ERROR("800101", "协议错误（客户到质检模块的请求协议错误）"),

    VIDEO_PROCESSING_ERROR("800401", "音频/视频模块处理错误"),

    ABSENCE_DETERMINE_ERROR("800601", "离席判定错误"),

    REMOTE_PROCESSING_VOICE_ERROR("800201", "语音错误"),

    SENSITIVE_WORDS_FILTERING_ERROR("800501", "话术/敏感词模块错误"),

    OTHER_ERROR("800001", "其他错误"),

    DFS_DOWNLOAD_ERROR("800002", "fastDFS下载错误");

    private String transResultCode;

    private String transResultDesc;
}
