#!/bin/bash
#
source /etc/profile
source ~/.bashrc

workDir="$(cd "$(dirname "$0")" && pwd)"
#jni程序位置
soPath=${workDir}/bin/libhw_ocr.so
#初始化文件夹所在位置
initPath=${workDir}/bin/data/json/
#依赖库文件夹位置
libPath=${workDir}/bin/deps/

function prepare() {
	ulimit -c 0
	export LD_LIBRARY_PATH=${libPath}:$LD_LIBRARY_PATH
	startCmd="java -jar ${workDir}/video-recognition-service.jar --config.jarPath=${workDir} --config.soPath=${soPath} --config.initPath=${initPath} --spring.profiles.active=test"
}

function stop() {
	check
	if [ $? == 11 ]; then
		echo 'Already stopped.'
		exit 0
	else
		echo 'Attempt stopping...'
	fi
	java_pid=$(ps axu|grep video-recognition-service.jar |grep -v grep|grep ${workDir}|awk '{print $2;}' | head -n 1)
	if [ "P${java_pid}" != "P" ]; then
		agent_pid=$(ps axu|grep yunteller_agent|grep -v grep |grep ${java_pid}|awk '{print $2;}' | head -n 1)
	fi

	#killing jar
	echo "Killing service, pid=${java_pid}"
	kill -9 ${java_pid}
	sleep 3

	if [ "P${agent_pid}" != "P" ]; then
		echo "killing yunteller_agent, pid=${agent_pid}"
		kill -9 ${agent_pid}
		sleep 3
	fi

	pid=$(ps axu|grep yunteller_agent|grep -v grep |grep ${java_pid}|awk '{print $2;}' | head -n 1)
	if [ "P${pid}" == "P" ]; then
		echo "yunteller_agent is now stopped. pid=${agent_pid}"
	else
		echo "yunteller_agent failed to stop. pid=${agent_pid}"
	fi

	pid=$(ps axu|grep video-recognition-service.jar |grep -v grep|grep ${workDir}|awk '{print $2;}' | head -n 1)
	if [ "P${pid}" == "P" ]; then
		echo "Service is now stopped."
	else
		echo "Service stopping has failed. pid=${pid}"
	fi


}

function check() 
{
	local java_pid
	local agent_id
	java_pid=$(ps axu|grep video-recognition-service.jar |grep -v grep|grep ${workDir}|awk '{print $2;}' | head -n 1)


	if [ "P${java_pid}" != "P" ]; then
		agent_pid=$(ps axu|grep yunteller_agent|grep -v grep |grep ${java_pid}|awk '{print $2;}' | head -n 1)
	fi
	

	if [ "P${agent_pid}" == "P" ]; then
		echo "yunteller_agent is not running."
	else
		echo "yunteller_agent is running, pid=${agent_pid}"
	fi

	if [ "P${java_pid}" ==  "P" ]; then
		echo "Service is not running."
		return 11
	else
		echo "Service is running, pid=${java_pid}"
		return 10
	fi
}

function start() {
	echo "Attempt to start......"
	eval $startCmd
}

function backStart() {
	echo "Attempt to start......"
	eval nohup $startCmd >/dev/null 2>&1 &
	echo "Service has been launched. check info.log later to ensure whether the service started successfully."
}

function showLog() {
	log=${workDir}/log/info.log
	if [ -f "${log}" ]; then
		tail -fn100 ${log}
	else
		echo "No logfile:${log}"
	fi
}

function cleanLogs() {
	cd ${workDir}
	find . -type f -name "*.log*" -exec rm {} \;
	find . -type f -name "*.out*" -exec rm {} \;
	find . -type f -name "core.*" -exec rm {} \;
	echo "Logfile has been cleaned up."
}

function make() {
	echo "Attempt to make tgz......"
	cd ${workDir}
	projectName="${workDir##*/}"
	tgz="$projectName.tar.gz"
	echo "Tgz file name is $tgz"
	cd ..
	if [ -f "${tgz}" ]; then
		time=$(date +%Y%m%d)
		mv ${tgz} ${tgz}.${time}
	fi
	tar -zcvf "${tgz}" "${projectName}"
}

function usage() {
	echo "usage: $0 command"
	echo ""
	echo "command is the one of:"
	echo "  start: start the service on foreground"
	echo "  nohup: start the service on background"
	echo "  stop: stop the service"
	echo "  check: check if the service is running"
	echo "  showlog: show info.log"
	echo "  cleanlogs: remove all service logs"
	echo "  make: make service compressed package(tgz)"
}

function main() {
	if [ -n "$1" ]; then
		if [ $1 == "stop" ]; then
			stop
      check
      echo 0
    elif [ $1 == "check" ]; then
      check
      echo 0
    elif [ $1 == "start" ]; then
      cd ${workDir}
      prepare
      start
    elif [ $1 == "nohup" ]; then
      check
      if [ $? == 10 ]; then
        stop
      fi
      cd ${workDir}
      prepare
      backStart
      echo 0
    elif [ $1 == "showlog" ]; then
      showLog
      echo 0
    elif [ $1 == "cleanlogs" ]; then
      cleanLogs
      echo 0
    elif [ $1 == "make" ]; then
      cleanLogs
      make
      echo 0
    else
      usage
    fi
  else
    usage
  fi
}
main $1
