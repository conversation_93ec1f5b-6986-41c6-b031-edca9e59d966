package com.hanvon.recognition.util;

import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.domain.proto.storage.DownloadByteArray;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.File;

@Component
@RequiredArgsConstructor
public class DfsUtils {

    private final FastFileStorageClient fastFileStorageClient;

    @SneakyThrows
    public String uploadFile(byte[] bytes, String extension) {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        StorePath storePath = fastFileStorageClient.uploadFile("group1", byteArrayInputStream, bytes.length, extension);
        return storePath.getFullPath();
    }

    @SneakyThrows
    public byte[] downloadFile(String filePath) {
        String group = filePath.substring(0, filePath.indexOf("/"));
        String path = filePath.substring(filePath.indexOf("/") + 1);
        return fastFileStorageClient.downloadFile(group, path, new DownloadByteArray());

    }

    @SneakyThrows
    public String downloadFile(String filePath, String fileName, String folder) {
        String fileLocalPath = folder + fileName;
        FileUtils.writeByteArrayToFile(new File(fileLocalPath), downloadFile(filePath));
        return fileLocalPath;
    }

    public void deleteFile(String filePath) {
        fastFileStorageClient.deleteFile(filePath);
    }
}
