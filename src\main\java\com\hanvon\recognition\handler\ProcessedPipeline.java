package com.hanvon.recognition.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hanvon.recognition.config.SystemConfig;
import com.hanvon.recognition.jni.JniApi;
import com.hanvon.recognition.pojo.*;
import com.hanvon.recognition.pojo.jni.*;
import com.hanvon.recognition.pojo.voice.Asr;
import com.hanvon.recognition.util.CommonUtils;
import com.hanvon.recognition.util.CompletableFutureExpandUtils;
import com.hanvon.recognition.util.DfsUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@Component
@RequiredArgsConstructor
@Slf4j
public class ProcessedPipeline {

    private final DfsUtils dfsUtils;

    private final ObjectMapper objectMapper;

    private final VoiceRemoteProcessor voiceRemoteProcessor;

    private final SystemConfig systemConfig;

    public ResponseDto process(ResponseDto responseDto) throws InterruptedException, ExecutionException {
        final String tempPath = systemConfig.getJarPath() + File.separator + "temp" + File.separator;
        AtomicReference<ResponseDto> responseDtoAtom = new AtomicReference<>(responseDto);
        String transId = responseDto.getTransId();
        String transUuid = responseDto.getTransUuid();
        FileInfo fileInfo = responseDto.getFileInfo();
        QcInfo qcInfo = responseDto.getQcInfo();
        try {
            CompletableFuture<Pair<String, String>> videoDownloadTask = CompletableFutureExpandUtils.orTimeout(CompletableFuture.supplyAsync(() -> {
                LocalDateTime startTime = LocalDateTime.now();
                try {
                    log.info("{},开始下载视频文件", responseDto.print());
                    String videoFileLocalPath = dfsUtils.downloadFile(fileInfo.getVideoFilePath(), fileInfo.getVideoFileName(), tempPath);
                    String outputPath = tempPath + FilenameUtils.getBaseName(fileInfo.getVideoFileName()) + File.separator;
                    log.info("{},下载视频文件完成", responseDto.print());
                    return new ImmutablePair<>(videoFileLocalPath, outputPath);
                } catch (Exception e) {
                    log.error("{},下载视频文件失败:{}", responseDto.print(), CommonUtils.exceptionToString(e));
                    responseDtoAtom.get().setFail(ErrorResult.DFS_DOWNLOAD_ERROR);
                    return null;
                } finally {
                    log.info("{},下载视频文件流程结束,耗时{}", responseDto.print(), CommonUtils.tdFormat(startTime, LocalDateTime.now()));
                }
            }), 30, TimeUnit.MINUTES);

            CompletableFuture<VideoProcessingResponseDto> videoProcessingTask = CompletableFutureExpandUtils.orTimeout(videoDownloadTask.thenApplyAsync((pair) -> {
                if (responseDtoAtom.get().isFail()) {
                    log.info("{},前置流程失败,跳过调用核心音视频分离流程", responseDto.print());
                    return null;
                }
                LocalDateTime startTime = LocalDateTime.now();
                try {
                    String content = objectMapper.writeValueAsString(new VideoProcessingRequestDto(transId, transUuid, pair.getLeft(), pair.getRight(), qcInfo));
                    log.info("{},开始调用核心音视频分离,请求json:{}", responseDto.print(), content);
                    String videoProcessingResponse = JniApi.videoProcessing(JniApi.handle, content);
                    log.info("{},调用核心音视频分离结束,响应json:{}", responseDto.print(), videoProcessingResponse);
                    VideoProcessingResponseDto videoProcessingResponseDto = objectMapper.readValue(videoProcessingResponse, VideoProcessingResponseDto.class);
                    if (!StringUtils.equals("000000", videoProcessingResponseDto.getTransResultCode())) {
                        log.error("{},调用核心音视频分离失败,核心返回code为:{}", responseDto.print(), videoProcessingResponseDto.getTransResultCode());
                        responseDtoAtom.get().setFail(videoProcessingResponseDto.getTransResultCode(), videoProcessingResponseDto.getTransResultDesc());
                        return null;
                    }
                    log.info("{},调用核心音视频分离完成", responseDto.print());
                    return videoProcessingResponseDto;
                } catch (Exception e) {
                    log.error("{},调用核心音视频分离失败:{}", responseDto.print(), CommonUtils.exceptionToString(e));
                    responseDtoAtom.get().setFail(ErrorResult.VIDEO_PROCESSING_ERROR);
                    return null;
                } finally {
                    log.info("{},调用核心音视频分离流程结束,耗时{}", responseDto.print(), CommonUtils.tdFormat(startTime, LocalDateTime.now()));
                }
            }), 30, TimeUnit.MINUTES);

            CompletableFuture<AbsenceDetermineResponseDto> absenceDetermineTask = CompletableFutureExpandUtils.orTimeout(videoProcessingTask.thenApplyAsync(videoProcessingResponseDto -> {
                if (responseDtoAtom.get().isFail()) {
                    log.info("{},前置流程失败,跳过调用核心离席判定流程", responseDto.print());
                    return null;
                }
                LocalDateTime startTime = LocalDateTime.now();
                try {
                    String content = objectMapper.writeValueAsString(new AbsenceDetermineRequestDto(transId, transUuid, videoProcessingResponseDto.getProcessedVideoPath(), qcInfo));
                    log.info("{},开始调用核心离席判定,请求json:{}", responseDto.print(), content);
                    String absenceDetermineResponse = JniApi.absenceDetermine(JniApi.handle, content);
                    log.info("{},调用核心离席判定结束,响应json:{}", responseDto.print(), absenceDetermineResponse);
                    AbsenceDetermineResponseDto absenceDetermineResponseDto = objectMapper.readValue(absenceDetermineResponse, AbsenceDetermineResponseDto.class);
                    absenceDetermineResponseDto.setVideoDuration(videoProcessingResponseDto.getVideoDuration());
                    for (Absence absence : absenceDetermineResponseDto.getAbsenceList()) {
                        String startPicPath = dfsUtils.uploadFile(FileUtils.readFileToByteArray(new File(absence.getStartPicPath())), FilenameUtils.getExtension(absence.getStartPicPath()));
                        String endPicPath = dfsUtils.uploadFile(FileUtils.readFileToByteArray(new File(absence.getEndPicPath())), FilenameUtils.getExtension(absence.getEndPicPath()));
                        absence.setStartPicPath(startPicPath);
                        absence.setEndPicPath(endPicPath);
                    }
                    if (!StringUtils.equals("000000", absenceDetermineResponseDto.getTransResultCode())) {
                        log.error("{},调用核心离席判定失败,核心返回code为:{}", responseDto.print(), videoProcessingResponseDto.getTransResultCode());
                        responseDtoAtom.get().setFail(absenceDetermineResponseDto.getTransResultCode(), absenceDetermineResponseDto.getTransResultDesc());
                        return null;
                    }
                    log.info("{},调用核心离席判定完成", responseDto.print());
                    return absenceDetermineResponseDto;
                } catch (Exception e) {
                    log.error("{},调用核心离席判定失败:{}", responseDto.print(), CommonUtils.exceptionToString(e));
                    responseDtoAtom.get().setFail(ErrorResult.ABSENCE_DETERMINE_ERROR);
                    return null;
                } finally {
                    log.info("{},调用核心离席判定流程结束,耗时{}", responseDto.print(), CommonUtils.tdFormat(startTime, LocalDateTime.now()));

                }
            }), 30, TimeUnit.MINUTES);

            CompletableFuture<Asr> remoteProcessingVoiceTask = CompletableFutureExpandUtils.orTimeout(videoProcessingTask.thenApplyAsync(videoProcessingResponseDto -> {
                if (responseDtoAtom.get().isFail()) {
                    log.info("{},前置流程失败,跳过调用远程语音转文本流程", responseDto.print());
                    return null;
                }
                LocalDateTime startTime = LocalDateTime.now();
                try {
                    log.info("{},开始调用远程语音转文本", responseDto.print());
                    Asr asr = voiceRemoteProcessor.remoteProcessingVoice(responseDto, videoProcessingResponseDto.getProcessedAudioPath());
                    if (Objects.isNull(asr)) {
                        responseDtoAtom.get().setFail(ErrorResult.REMOTE_PROCESSING_VOICE_ERROR);
                    }
                    return asr;
                } catch (Exception e) {
                    log.error("{},调用远程语音转文本失败:{}", responseDto.print(), CommonUtils.exceptionToString(e));
                    responseDtoAtom.get().setFail(ErrorResult.REMOTE_PROCESSING_VOICE_ERROR);
                    return null;
                } finally {
                    log.info("{},调用远程语音转文本流程结束,耗时{}", responseDto.print(), CommonUtils.tdFormat(startTime, LocalDateTime.now()));
                }
            }), 30, TimeUnit.MINUTES);

            CompletableFuture<SensitiveWordsFilteringResponseDto> sensitiveWordsFilteringTask = CompletableFutureExpandUtils.orTimeout(remoteProcessingVoiceTask.thenApplyAsync(asr -> {
                if (responseDtoAtom.get().isFail()) {
                    log.info("{},前置流程失败,跳过调用核心敏感词过滤流程", responseDto.print());
                    return null;
                }
                LocalDateTime startTime = LocalDateTime.now();
                try {
                    List<SensWordTemplate> list = objectMapper.readValue(new String(dfsUtils.downloadFile(fileInfo.getSensWordTemplateFilePath()), StandardCharsets.UTF_8), new TypeReference<List<SensWordTemplate>>() {
                    });
                    String content = objectMapper.writeValueAsString(new SensitiveWordsFilteringRequestDto(transId, transUuid, list, asr, qcInfo));
                    log.info("{},开始调用核心敏感词过滤,请求json:{}", responseDto.print(), content);
                    String sensitiveWordsFilteringResponse = JniApi.sensitiveWordsFiltering(JniApi.handle, content);
                    log.info("{},调用核心敏感词过滤结束,响应json:{}", responseDto.print(), sensitiveWordsFilteringResponse);
                    SensitiveWordsFilteringResponseDto sensitiveWordsFilteringResponseDto = objectMapper.readValue(sensitiveWordsFilteringResponse, SensitiveWordsFilteringResponseDto.class);
                    if (!StringUtils.equals("000000", sensitiveWordsFilteringResponseDto.getTransResultCode())) {
                        log.error("{},调用核心敏感词过滤失败,核心返回code为:{}", responseDto.print(), sensitiveWordsFilteringResponseDto.getTransResultCode());
                        responseDtoAtom.get().setFail(sensitiveWordsFilteringResponseDto.getTransResultCode(), sensitiveWordsFilteringResponseDto.getTransResultDesc());
                        return null;
                    }
                    log.info("{},调用核心敏感词过滤完成", responseDto.print());
                    return sensitiveWordsFilteringResponseDto;
                } catch (Exception e) {
                    log.error("{},调用核心敏感词过滤失败:{}", responseDto.print(), CommonUtils.exceptionToString(e));
                    responseDtoAtom.get().setFail(ErrorResult.SENSITIVE_WORDS_FILTERING_ERROR);
                    return null;
                } finally {
                    log.info("{},调用核心敏感词过滤流程结束,耗时{}", responseDto.print(), CommonUtils.tdFormat(startTime, LocalDateTime.now()));
                }
            }), 30, TimeUnit.MINUTES);

            return absenceDetermineTask.thenCombine(sensitiveWordsFilteringTask, (absenceDetermineResponseDto, sensitiveWordsFilteringResponseDto) -> {
                ResponseDto dto = responseDtoAtom.get();
                if (dto.isFail()) {
                    return dto;
                }
                dto.setVideoDuration(absenceDetermineResponseDto.getVideoDuration());
                dto.setAbsenceList(absenceDetermineResponseDto.getAbsenceList());
                dto.setAsrText(sensitiveWordsFilteringResponseDto.getAsrText());
                dto.setSensitiveList(sensitiveWordsFilteringResponseDto.getSensitiveList());
                return dto;
            }).get();
        } finally {
            //删除临时文件
            if (systemConfig.getDeleteTemp()) {
                FileUtils.deleteQuietly(new File(tempPath));
            }
        }
    }
}
