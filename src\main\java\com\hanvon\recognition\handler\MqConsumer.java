package com.hanvon.recognition.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hanvon.recognition.config.RabbitConfig;
import com.hanvon.recognition.pojo.*;
import com.hanvon.recognition.util.CommonUtils;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Component
@RequiredArgsConstructor
@Slf4j
public class MqConsumer {

    private final ObjectMapper objectMapper;

    private final ProcessedPipeline processedPipeline;

    private final MqProducer mqProducer;

    @SneakyThrows
    @RabbitListener(id = "consumer", autoStartup = "false", queues = RabbitConfig.QUEUE_REQUEST, ackMode = "MANUAL")
    public void consume(Message message, Channel channel) {
        LocalDateTime startTime = LocalDateTime.now();
        try {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.info("消息确认失败:{}", CommonUtils.exceptionToString(e));
        }
        ResponseDto responseDto = new ResponseDto();
        try {
            try {
                String request = new String(message.getBody(), StandardCharsets.UTF_8).replaceAll("[\t\n\r]", "");
                log.info("接收到消息:{}", request);
                RequestDto requestDto = objectMapper.readValue(request, RequestDto.class);
                String transId = requestDto.getTransId();
                String transUuid = requestDto.getTransUuid();
                FileInfo fileInfo = requestDto.getFileInfo();
                QcInfo qcInfo = requestDto.getQcInfo();
                responseDto.init(transId, transUuid, fileInfo, qcInfo);
                if (StringUtils.isAnyBlank(transId, transUuid, fileInfo.getVideoFileName(), fileInfo.getVideoFilePath(), fileInfo.getSensWordTemplateFileName(), fileInfo.getSensWordTemplateFilePath(), qcInfo.getVideoType())) {
                    log.error("{},请求报文中有属性值为空", responseDto.print());
                    responseDto.setFail(ErrorResult.REQUEST_CONVERSION_ERROR);
                    return;
                }
            } catch (Exception e) {
                log.error("{},请求报文转换失败:{}", responseDto.print(), CommonUtils.exceptionToString(e));
                responseDto.setFail(ErrorResult.REQUEST_CONVERSION_ERROR);
                return;
            }
            try {
                responseDto = processedPipeline.process(responseDto);
            } catch (Exception e) {
                log.error("{},pipeline处理失败:{}", responseDto.print(), CommonUtils.exceptionToString(e));
                responseDto.setFail(ErrorResult.OTHER_ERROR);
            }
        } finally {
            try {
                responseDto.setStartTime(Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()));
                LocalDateTime endTime = LocalDateTime.now();
                responseDto.setEndTime(Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));
                String response = objectMapper.writeValueAsString(responseDto);
                log.info("{},全流程结束,共耗时{},向mq推送结果:{}", responseDto.print(), CommonUtils.tdFormat(startTime, endTime), response);
                mqProducer.send(response);
            } catch (Exception e) {
                log.error("{},向mq推送结果失败:{}", responseDto.print(), CommonUtils.exceptionToString(e));
            }
        }
    }
}
