package com.hanvon.recognition.handler;

import com.hanvon.recognition.config.RabbitConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class MqProducer {

    private final RabbitTemplate rabbitTemplate;

    public void send(String content) {
        rabbitTemplate.convertAndSend(RabbitConfig.EXCHANGE, RabbitConfig.ROUTING_KEY_RESPONSE, content);
    }
}
