package com.hanvon.recognition.cronjob;

import com.hanvon.recognition.config.SystemConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.TrueFileFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeleteCronJob {

    private final SystemConfig systemConfig;

    @Scheduled(cron = "0 0 0 * * ? ")
    public void delete() {
        try {
            LocalDate needDeleteDays = LocalDate.now().plusDays(-systemConfig.getLogSaveDays());
            FileUtils.listFiles(new File(systemConfig.getJarPath()), TrueFileFilter.INSTANCE, TrueFileFilter.INSTANCE).stream().filter(file -> StringUtils.contains(file.getName(), ".log.")).filter(file -> {
                String before = StringUtils.substringBefore(file.getName(), ".");
                String substring = StringUtils.substring(before, before.length() - 8, before.length());
                if (!StringUtils.isNumeric(substring)) {
                    return false;
                }
                LocalDate fileTime = LocalDate.parse(substring, DateTimeFormatter.ofPattern("yyyyMMdd"));
                return fileTime.isBefore(needDeleteDays);
            }).forEach(FileUtils::deleteQuietly);
        } catch (Exception e) {
            log.info("清理日志异常:{}", e.getMessage());
        }
    }
}
