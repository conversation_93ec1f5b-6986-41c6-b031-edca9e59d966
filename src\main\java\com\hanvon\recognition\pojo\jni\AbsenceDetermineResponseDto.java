package com.hanvon.recognition.pojo.jni;

import com.hanvon.recognition.pojo.Absence;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class AbsenceDetermineResponseDto {
    private String transId;
    private String transUuid;
    private String transResultCode;
    private String transResultDesc;
    private String videoDuration;
    private List<Absence> absenceList;
}
