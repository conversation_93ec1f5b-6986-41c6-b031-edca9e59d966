package com.hanvon.recognition.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ResponseDto {
    private String transId;
    private String transUuid;
    private FileInfo fileInfo;
    @JsonIgnore
    private QcInfo qcInfo;
    private String transStatus;
    private String transResultCode;
    private String transResultDesc;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endTime;
    private String videoDuration;
    private List<Absence> absenceList;
    private String asrText;
    private List<Sensitive> sensitiveList;

    public void setFail(String transResultCode, String transResultDesc) {
        this.setTransResultCode(transResultCode);
        this.setTransResultDesc(transResultDesc);
        this.setTransStatus("2");
    }

    public void init(String transId, String transUuid, FileInfo fileInfo, QcInfo qcInfo) {
        this.setTransId(transId);
        this.setTransUuid(transUuid);
        this.setFileInfo(fileInfo);
        this.setQcInfo(qcInfo);
        this.setTransStatus("1");
        this.setTransResultCode("000000");
        this.setTransResultDesc("success");
    }

    @JsonIgnore
    public String print() {
        return "transId:" + this.getTransId() + ",transUuid:" + this.getTransUuid();
    }

    @JsonIgnore
    public boolean isFail() {
        return !StringUtils.equals(this.getTransResultCode(), "000000");
    }

    public void setFail(ErrorResult errorResult) {
        this.setTransResultCode(errorResult.getTransResultCode());
        this.setTransResultDesc(errorResult.getTransResultDesc());
        this.setTransStatus("2");
    }
}
