package com.hanvon.recognition.pojo.jni;

import com.hanvon.recognition.pojo.Sensitive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class SensitiveWordsFilteringResponseDto {
    private String transId;
    private String transUuid;
    private String transResultCode;
    private String transResultDesc;
    private String asrText;
    private List<Sensitive> sensitiveList;
}
