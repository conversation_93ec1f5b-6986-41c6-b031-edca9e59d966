package com.hanvon.recognition;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hanvon.recognition.pojo.ResponseDto;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class TestClass {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    @SneakyThrows
    public static void main(String[] args) {

    }

    @Test
    @SneakyThrows
    public void test() {
        ResponseDto responseDto = new ResponseDto();
        LocalDateTime endTime = LocalDateTime.now();
        responseDto.setEndTime(Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));
        String response = objectMapper.writeValueAsString(responseDto);
        log.info("发送消息:{}", response);
    }
}
