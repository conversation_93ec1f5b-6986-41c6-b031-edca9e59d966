package com.hanvon.recognition.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "config")
public class SystemConfig {
    private String jarPath;
    private String soPath;
    private String initPath;
    private Long logSaveDays;
    private Boolean deleteTemp;
}
